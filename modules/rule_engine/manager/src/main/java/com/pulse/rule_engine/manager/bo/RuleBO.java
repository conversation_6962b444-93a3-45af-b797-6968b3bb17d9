package com.pulse.rule_engine.manager.bo;

import com.pulse.rule_engine.manager.bo.base.BaseRuleBO;
import com.pulse.rule_engine.manager.adapter.RuleEngineAdapterFactory;
import com.pulse.rule_engine.manager.adapter.RuleEngineAdapter;
import com.pulse.rule_engine.manager.dto.RuleVersionBaseDto;
import com.pulse.rule_engine.common.enums.RuleStatusEnum;
import com.pulse.rule_engine.common.enums.RuleOrgRelationTypeEnum;
import com.pulse.pulse.common.utils.SemanticVersionUtils;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;
import cn.hutool.extra.spring.SpringUtil;

import lombok.extern.slf4j.Slf4j;

import org.springframework.util.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.HashSet;
import java.util.stream.Collectors;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;

@DynamicInsert
@Where(clause = "deleted_at = 0 ")
@SQLDelete(
        sql =
                "UPDATE rule  SET deleted_at = (EXTRACT(DAY FROM (CURRENT_TIMESTAMP -"
                    + " TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 24 * 60 *"
                    + " 60 * 1000 + EXTRACT(HOUR FROM (CURRENT_TIMESTAMP - TO_TIMESTAMP('1970-01-01"
                    + " 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 60 * 60 * 1000 + EXTRACT(MINUTE"
                    + " FROM (CURRENT_TIMESTAMP - TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD"
                    + " HH24:MI:SS'))) * 60 * 1000 + EXTRACT(SECOND FROM (CURRENT_TIMESTAMP -"
                    + " TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 1000)"
                    + " WHERE id = ? and lock_version = ?")
@Slf4j
@Table(name = "rule")
@Entity
@AutoGenerated(locked = false, uuid = "7f3e312f-a4ff-484e-bc1d-dd4cb00ec2a6|BO|DEFINITION")
public class RuleBO extends BaseRuleBO {

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true, uuid = "7f3e312f-a4ff-484e-bc1d-dd4cb00ec2a6|BO|AGG|VALIDATOR")
    @Override
    protected void validateAggregate() {
        log.debug("开始校验规则聚合数据，规则ID: {}, 规则编码: {}", this.getId(), this.getCode());

        // 1. 聚合根基础校验
        validateRuleBasicFields();

        // 2. 规则状态一致性校验
        validateRuleStatusConsistency();

        // 3. 当前版本一致性校验
        validateCurrentVersionConsistency();

        // 4. 规则版本聚合校验
        validateRuleVersionsAggregate();

        // 5. 规则组织关系校验
        validateRuleOrganizationsAggregate();

        // 6. 规则冲突检测
        validateRuleConflicts();

        log.debug("规则聚合数据校验完成");
    }

    /**
     * 聚合根基础字段校验
     */
    private void validateRuleBasicFields() {
        // 校验规则编码不能为空
        if (!StringUtils.hasText(this.getCode())) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "规则编码不能为空");
        }

        // 校验显示名称不能为空
        if (!StringUtils.hasText(this.getDisplayName())) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "规则显示名称不能为空");
        }

        // 校验规则状态不能为空
        if (this.getStatus() == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "规则状态不能为空");
        }

        // 校验规则分类ID不能为空
        if (!StringUtils.hasText(this.getRuleCategoryId())) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "规则分类ID不能为空");
        }
    }

    /**
     * 规则状态一致性校验
     */
    private void validateRuleStatusConsistency() {
        RuleStatusEnum status = this.getStatus();
        Set<RuleVersionBO> versions = this.getRuleVersionBOSet();

        // 如果规则状态为生效，必须有当前版本
        if (RuleStatusEnum.EFFECTIVE.equals(status)) {
            if (!StringUtils.hasText(this.getCurrentVersion())) {
                throw new IgnoredException(ErrorCode.WRONG_PARAMETER,
                    "规则状态为生效时，必须指定当前版本");
            }

            // 检查当前版本是否存在于版本集合中
            if (!CollectionUtils.isEmpty(versions)) {
                boolean currentVersionExists = versions.stream()
                    .anyMatch(v -> this.getCurrentVersion().equals(v.getVersionNumber()));

                if (!currentVersionExists) {
                    throw new IgnoredException(ErrorCode.WRONG_PARAMETER,
                        String.format("当前版本 [%s] 在规则版本集合中不存在", this.getCurrentVersion()));
                }
            }
        }

        // 如果规则状态为草稿或测试，当前版本可以为空
        if (RuleStatusEnum.DRAFT.equals(status) || RuleStatusEnum.TEST.equals(status)) {
            if (StringUtils.hasText(this.getCurrentVersion())) {
                log.warn("规则状态为 [{}] 时，通常不需要设置当前版本", status);
            }
        }

        // 如果规则状态为已下线，检查是否还有生效的版本
        if (RuleStatusEnum.OFFLINE.equals(status)) {
            if (!CollectionUtils.isEmpty(versions)) {
                Date now = new Date();
                boolean hasActiveVersion = versions.stream()
                    .anyMatch(v -> v.getEffectiveStartTime() != null
                        && v.getEffectiveStartTime().before(now)
                        && (v.getEffectiveEndTime() == null || v.getEffectiveEndTime().after(now)));

                if (hasActiveVersion) {
                    log.warn("规则状态为已下线，但仍有生效的版本存在");
                }
            }
        }
    }

    /**
     * 当前版本一致性校验
     */
    private void validateCurrentVersionConsistency() {
        String currentVersion = this.getCurrentVersion();
        if (!StringUtils.hasText(currentVersion)) {
            return; // 当前版本为空时跳过校验
        }

        // 校验当前版本号格式
        if (!SemanticVersionUtils.isValidVersion(currentVersion)) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER,
                String.format("当前版本号 [%s] 格式不正确，应遵循语义化版本规范", currentVersion));
        }

        Set<RuleVersionBO> versions = this.getRuleVersionBOSet();
        if (!CollectionUtils.isEmpty(versions)) {
            // 检查当前版本是否存在
            RuleVersionBO currentVersionBO = versions.stream()
                .filter(v -> currentVersion.equals(v.getVersionNumber()))
                .findFirst()
                .orElse(null);

            if (currentVersionBO == null) {
                throw new IgnoredException(ErrorCode.WRONG_PARAMETER,
                    String.format("当前版本 [%s] 在版本集合中不存在", currentVersion));
            }

            // 检查当前版本是否为最新版本
            String latestVersion = getLatestVersionFromSet(versions);
            if (StringUtils.hasText(latestVersion) && !currentVersion.equals(latestVersion)) {
                log.warn("当前版本 [{}] 不是最新版本，最新版本为 [{}]", currentVersion, latestVersion);
            }

            // 检查当前版本的生效时间
            Date now = new Date();
            if (currentVersionBO.getEffectiveStartTime() != null
                && currentVersionBO.getEffectiveStartTime().after(now)) {
                log.warn("当前版本 [{}] 的生效开始时间晚于当前时间", currentVersion);
            }

            if (currentVersionBO.getEffectiveEndTime() != null
                && currentVersionBO.getEffectiveEndTime().before(now)) {
                log.warn("当前版本 [{}] 的生效结束时间早于当前时间，版本可能已过期", currentVersion);
            }
        }
    }

    /**
     * 规则版本聚合校验
     */
    private void validateRuleVersionsAggregate() {
        Set<RuleVersionBO> versions = this.getRuleVersionBOSet();
        if (CollectionUtils.isEmpty(versions)) {
            // 如果规则状态为生效，必须至少有一个版本
            if (RuleStatusEnum.EFFECTIVE.equals(this.getStatus())) {
                throw new IgnoredException(ErrorCode.WRONG_PARAMETER,
                    "规则状态为生效时，必须至少有一个版本");
            }
            return;
        }

        // 检查版本号唯一性
        Set<String> versionNumbers = new HashSet<>();
        for (RuleVersionBO version : versions) {
            String versionNumber = version.getVersionNumber();
            if (StringUtils.hasText(versionNumber)) {
                if (versionNumbers.contains(versionNumber)) {
                    throw new IgnoredException(ErrorCode.WRONG_PARAMETER,
                        String.format("版本号 [%s] 在聚合中重复", versionNumber));
                }
                versionNumbers.add(versionNumber);
            }
        }

        // 检查版本时间重叠（聚合级别）
        validateVersionTimeOverlapInAggregate(versions);

        // 检查是否有生效版本与当前版本一致
        if (StringUtils.hasText(this.getCurrentVersion())) {
            boolean hasMatchingVersion = versions.stream()
                .anyMatch(v -> this.getCurrentVersion().equals(v.getVersionNumber()));

            if (!hasMatchingVersion) {
                throw new IgnoredException(ErrorCode.WRONG_PARAMETER,
                    String.format("当前版本 [%s] 在版本集合中不存在", this.getCurrentVersion()));
            }
        }
    }

    /**
     * 规则组织关系校验
     */
    private void validateRuleOrganizationsAggregate() {
        Set<RuleOrganizationBO> organizations = this.getRuleOrganizationBOSet();
        if (CollectionUtils.isEmpty(organizations)) {
            return; // 组织关系为空时跳过校验
        }

        // 检查组织ID唯一性（同一关系类型下）
        organizations.stream()
            .collect(Collectors.groupingBy(RuleOrganizationBO::getRelationType))
            .forEach((relationType, orgList) -> {
                Set<String> orgIds = new HashSet<>();
                for (RuleOrganizationBO org : orgList) {
                    String orgId = org.getOrganizationId();
                    if (StringUtils.hasText(orgId)) {
                        if (orgIds.contains(orgId)) {
                            throw new IgnoredException(ErrorCode.WRONG_PARAMETER,
                                String.format("组织ID [%s] 在关系类型 [%s] 下重复", orgId, relationType));
                        }
                        orgIds.add(orgId);
                    }
                }
            });

        // 检查白名单和黑名单冲突
        Set<String> whitelistOrgs = organizations.stream()
            .filter(org -> RuleOrgRelationTypeEnum.WHITELIST.equals(org.getRelationType()))
            .map(RuleOrganizationBO::getOrganizationId)
            .filter(StringUtils::hasText)
            .collect(Collectors.toSet());

        Set<String> blacklistOrgs = organizations.stream()
            .filter(org -> RuleOrgRelationTypeEnum.BLACKLIST.equals(org.getRelationType()))
            .map(RuleOrganizationBO::getOrganizationId)
            .filter(StringUtils::hasText)
            .collect(Collectors.toSet());

        // 检查是否有组织同时在白名单和黑名单中
        Set<String> conflictOrgs = new HashSet<>(whitelistOrgs);
        conflictOrgs.retainAll(blacklistOrgs);

        if (!conflictOrgs.isEmpty()) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER,
                String.format("组织 [%s] 同时存在于白名单和黑名单中", String.join(", ", conflictOrgs)));
        }
    }

    /**
     * 规则冲突检测
     */
    private void validateRuleConflicts() {
        Set<RuleVersionBO> versions = this.getRuleVersionBOSet();
        if (CollectionUtils.isEmpty(versions) || versions.size() < 2) {
            return; // 版本数量少于2个时跳过冲突检测
        }

        try {
            // 获取规则引擎适配器
            RuleEngineAdapterFactory adapterFactory = SpringUtil.getBean(RuleEngineAdapterFactory.class);
            if (adapterFactory != null) {
                RuleEngineAdapter adapter = adapterFactory.getDefaultAdapter();

                // 将版本BO转换为DTO进行冲突检测
                List<RuleVersionBaseDto> versionDtos = versions.stream()
                    .filter(v -> StringUtils.hasText(v.getDrlContent()))
                    .map(this::convertVersionToDto)
                    .collect(Collectors.toList());

                if (versionDtos.size() > 1) {
                    // 使用模拟业务数据进行冲突检测
                    String mockBusinessData = generateMockBusinessData();

                    Boolean hasConflict = adapter.checkRuleConflict(versionDtos, mockBusinessData);
                    if (Boolean.TRUE.equals(hasConflict)) {
                        log.warn("检测到规则版本间可能存在冲突，请检查规则逻辑");
                    }
                }
            }
        } catch (Exception e) {
            log.warn("规则冲突检测失败: {}", e.getMessage());
            // 冲突检测失败不阻止保存，只记录警告
        }
    }

    /**
     * 版本时间重叠校验（聚合级别）
     */
    private void validateVersionTimeOverlapInAggregate(Set<RuleVersionBO> versions) {
        List<RuleVersionBO> versionList = versions.stream()
            .filter(v -> v.getEffectiveStartTime() != null)
            .collect(Collectors.toList());

        for (int i = 0; i < versionList.size(); i++) {
            for (int j = i + 1; j < versionList.size(); j++) {
                RuleVersionBO version1 = versionList.get(i);
                RuleVersionBO version2 = versionList.get(j);

                if (isVersionTimeOverlap(version1, version2)) {
                    log.warn("检测到版本时间重叠：版本 [{}] 与版本 [{}] 的生效时间存在重叠",
                        version1.getVersionNumber(), version2.getVersionNumber());
                }
            }
        }
    }

    /**
     * 检查两个版本的时间是否重叠
     */
    private boolean isVersionTimeOverlap(RuleVersionBO version1, RuleVersionBO version2) {
        Date start1 = version1.getEffectiveStartTime();
        Date end1 = version1.getEffectiveEndTime();
        Date start2 = version2.getEffectiveStartTime();
        Date end2 = version2.getEffectiveEndTime();

        if (start1 == null || start2 == null) {
            return false;
        }

        // 如果没有结束时间，视为永久有效
        if (end1 == null) {
            end1 = new Date(Long.MAX_VALUE);
        }
        if (end2 == null) {
            end2 = new Date(Long.MAX_VALUE);
        }

        // 检查时间区间是否重叠
        return start1.before(end2) && end1.after(start2);
    }

    /**
     * 从版本集合中获取最新版本号
     */
    private String getLatestVersionFromSet(Set<RuleVersionBO> versions) {
        if (CollectionUtils.isEmpty(versions)) {
            return null;
        }

        return versions.stream()
            .map(RuleVersionBO::getVersionNumber)
            .filter(StringUtils::hasText)
            .filter(SemanticVersionUtils::isValidVersion)
            .max((v1, v2) -> SemanticVersionUtils.compareVersions(v1, v2))
            .orElse(null);
    }

    /**
     * 将版本BO转换为DTO
     */
    private RuleVersionBaseDto convertVersionToDto(RuleVersionBO versionBO) {
        RuleVersionBaseDto dto = new RuleVersionBaseDto();
        dto.setId(versionBO.getId());
        dto.setVersionNumber(versionBO.getVersionNumber());
        dto.setDrlContent(versionBO.getDrlContent());
        dto.setEffectiveStartTime(versionBO.getEffectiveStartTime());
        dto.setEffectiveEndTime(versionBO.getEffectiveEndTime());
        dto.setRuleId(this.getId());
        return dto;
    }

    /**
     * 生成模拟业务数据用于冲突检测
     */
    private String generateMockBusinessData() {
        // 生成HIS系统的模拟业务数据
        // 这里可以根据具体的业务场景生成更真实的数据
        return "{"
            + "\"patientId\":\"mock_patient_001\","
            + "\"departmentId\":\"mock_dept_001\","
            + "\"doctorId\":\"mock_doctor_001\","
            + "\"medicationCode\":\"mock_med_001\","
            + "\"dosage\":\"100mg\","
            + "\"frequency\":\"tid\","
            + "\"timestamp\":" + System.currentTimeMillis()
            + "}";
    }
}
